2025-05-30 15:18:04.2093 (AsyncETVoidMethodBuilder.cs:33) Error: 110313
Mao<PERSON><PERSON>J<PERSON>.RpcException: Rpc error: actorId: 1:-5:462113621778890765 request: MaoYouJi.GetAnnouceReq, response: { "_t" : "GetAnnouceResp", "RpcId" : 1, "RequestId" : 0, "UserId" : <PERSON><PERSON><PERSON>(0), "MsgTime" : <PERSON><PERSON><PERSON>(0), "Error" : 110313, "Message" : null, "showMessage" : null, "announcements" : null }
   at MaoYouJi.ETTask`1.GetResult() in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Shared_Part/ThirdParty/ETTask/ETTask.cs:line 261
   at MaoYouJi.MessageSenderStruct.Wait() in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Shared_Part/Core/Fiber/Module/Actor/MessageSenderStruct.cs:line 39
   at MaoYouJi.ETTask`1.GetResult() in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Shared_Part/ThirdParty/ETTask/ETTask.cs:line 261
   at MaoYouJi.ProcessInnerSenderSystem.Call(ProcessInnerSender self, ActorId actorId, IRequest request, Boolean needException) in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Shared_Part/Core/Fiber/Module/Actor/ProcessInnerSenderSystem.cs:line 188
   at MaoYouJi.ETTask`1.GetResult() in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Shared_Part/ThirdParty/ETTask/ETTask.cs:line 261
   at MaoYouJi.MessageSenderSystem.Call(MessageSender self, ActorId actorId, IRequest request, Boolean needException) in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Hotfix/Module/Message/MessageSenderSystem.cs:line 51
   at MaoYouJi.ETTask`1.GetResult() in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Shared_Part/ThirdParty/ETTask/ETTask.cs:line 261
   at MaoYouJi.NetComponentOnReadInvoker_Gate.HandleAsync(NetComponentOnRead args) in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Hotfix/ServerComp/Gate/NetComponentOnReadInvoker_Gate.cs:line 201
   at MaoYouJi.ETTask.InnerCoroutine() in /Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Shared_Part/ThirdParty/ETTask/ETTask.cs:line 75
