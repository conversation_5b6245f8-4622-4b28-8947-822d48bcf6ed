2025-05-30 15:16:52.3893 [f-0] StartAsync
2025-05-30 15:16:52.5440 [f-0] CreateCode: MaoYouJi.EntitySystemSingleton
2025-05-30 15:16:52.5440 [f-0] EntitySystemSingleton Awake: MaoYouJi.MailBoxComponentSystem+MaoYouJi_MailBoxComponent_MaoYouJi_MailBoxType_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.MailBoxComponentSystem+MaoYouJi_MailBoxComponent_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessInnerSenderSystem+MaoYouJi_ProcessInnerSender_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessInnerSenderSystem+MaoYouJi_ProcessInnerSender_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessInnerSenderSystem+MaoYouJi_ProcessInnerSender_UpdateSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockSystem+MaoYouJi_CoroutineLock_int_long_int_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockSystem+MaoYouJi_CoroutineLock_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockComponentSystem+MaoYouJi_CoroutineLockComponent_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockComponentSystem+MaoYouJi_CoroutineLockComponent_UpdateSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockQueueSystem+MaoYouJi_CoroutineLockQueue_int_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockQueueSystem+MaoYouJi_CoroutineLockQueue_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.CoroutineLockQueueTypeSystem+MaoYouJi_CoroutineLockQueueType_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.TimerComponentSystem+MaoYouJi_TimerComponent_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.TimerComponentSystem+MaoYouJi_TimerComponent_UpdateSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.ModeContexSystem+MaoYouJi_ModeContex_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.ModeContexSystem+MaoYouJi_ModeContex_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionSystem+MaoYouJi_Session_MaoYouJi_AService_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionSystem+MaoYouJi_Session_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.ObjectWaitSystem+MaoYouJi_ObjectWait_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.ObjectWaitSystem+MaoYouJi_ObjectWait_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.AccountSystem+MaoYouJi_Account_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.GateAccountsComponentSystem+MaoYouJi_GateAccountsComponent_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.DaTaoShaActCompSys+MaoYouJi_DaTaoShaActComp_MaoYouJi_DaTaoShaActConf_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserDaTaoShaInfoCompSystem+MaoYouJi_UserDaTaoShaInfoComp_MaoYouJi_DaTaoShaActComp_AwakeSystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserDaTaoShaInfoCompSystem+MaoYouJi_UserDaTaoShaInfoComp_DestroySystem
2025-05-30 15:16:52.5446 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackComponentSystem+MaoYouJi_AttackComponent_LoadSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackComponentSystem+MaoYouJi_AttackComponent_MaoYouJi_FightInfo_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackCtxCompSys+MaoYouJi_AttackCtxComp_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackCtxCompSys+MaoYouJi_AttackCtxComp_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackInCacheSys+MaoYouJi_AttackInCache_MaoYouJi_AttackComponent_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackInCacheSys+MaoYouJi_AttackInCache_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.ActiveJobInfoSystem+MaoYouJi_ActiveJobInfo_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.ActiveJobInfoSystem+MaoYouJi_ActiveJobInfo_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackSongSkillCompSys+MaoYouJi_AttackSongSkillComp_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackSongSkillCompSys+MaoYouJi_AttackSongSkillComp_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackStatusComponentSystem+MaoYouJi_AttackStatusComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AttackStatusComponentSystem+MaoYouJi_AttackStatusComponent_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneAttackStatusSystem+MaoYouJi_OneAttackStatus_MaoYouJi_AttachStatus_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneAttackStatusSystem+MaoYouJi_OneAttackStatus_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.InFightComponentSystem+MaoYouJi_InFightComponent_MaoYouJi_AttackComponent_MaoYouJi_AttackInCache_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.InFightComponentSystem+MaoYouJi_InFightComponent_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.InFightComponentSystem+MaoYouJi_InFightComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.KilledComponentSystem+MaoYouJi_KilledComponent_MaoYouJi_AttackComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.KilledComponentSystem+MaoYouJi_KilledComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.KilledComponentSystem+MaoYouJi_KilledComponent_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.NormalAttackCompSys+MaoYouJi_NormalAttackComp_long_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.NormalAttackCompSys+MaoYouJi_NormalAttackComp_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.ChatManageCompSys+MaoYouJi_ChatManageComp_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.EquipComponentSystem+MaoYouJi_EquipComponent_LoadSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.EquipComponentSystem+MaoYouJi_EquipComponent_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.GlobalManageCompSys+MaoYouJi_GlobalManageComp_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.TiaoSaoManageCompSys+MaoYouJi_TiaoSaoManageComp_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.FiberMapManageSystem+MaoYouJi_FiberMapManage_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AutoMoveComponentSystem+MaoYouJi_AutoMoveComponent_long_string_string_AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.AutoMoveComponentSystem+MaoYouJi_AutoMoveComponent_DestroySystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.GenMonComponentSystem+MaoYouJi_GenMonComponent_ConcurrentCollections_ConcurrentHashSet_MaoYouJi_MonsterGen__System_Collections_Concurrent_ConcurrentDictionary_MaoYouJi_MonBaseType_int__AwakeSystem
2025-05-30 15:16:52.5456 [f-0] EntitySystemSingleton Awake: MaoYouJi.MapAttackManageSys+MaoYouJi_MapAttackManage_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MapNodeSystem+MaoYouJi_MapNode_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MoveComponentSystem+MaoYouJi_MoveComponent_string_string_int_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.LockInfoSystem+MaoYouJi_LockInfo_MaoYouJi_ActorId_MaoYouJi_CoroutineLock_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.LockInfoSystem+MaoYouJi_LockInfo_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.LocationOneTypeSystem+MaoYouJi_LocationOneType_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.LocationComoponentSystem+MaoYouJi_LocationManagerComoponent_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderComponentSystem+MaoYouJi_MessageLocationSenderOneType_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderComponentSystem+MaoYouJi_MessageLocationSenderOneType_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderManagerComponentSystem+MaoYouJi_MessageLocationSenderComponent_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderSystem+MaoYouJi_MessageLocationSender_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageLocationSenderSystem+MaoYouJi_MessageLocationSender_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MessageRepeatSenderSystem+MaoYouJi_MessageRepeatSendComponent_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.CellSystem+MaoYouJi_Cell_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.CellSystem+MaoYouJi_Cell_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.DBComponentSystem+MaoYouJi_DBComponent_string_string_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.HttpComponentSystem+MaoYouJi_HttpComponent_string_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.HttpComponentSystem+MaoYouJi_HttpComponent_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessOuterSenderSystem+MaoYouJi_ProcessOuterSender_System_Net_IPEndPoint_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessOuterSenderSystem+MaoYouJi_ProcessOuterSender_UpdateSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.ProcessOuterSenderSystem+MaoYouJi_ProcessOuterSender_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterComponentSystem+MaoYouJi_RouterComponent_System_Net_IPEndPoint_string_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterComponentSystem+MaoYouJi_RouterComponent_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterComponentSystem+MaoYouJi_RouterComponent_UpdateSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterNodeSystem+MaoYouJi_RouterNode_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.RouterNodeSystem+MaoYouJi_RouterNode_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_MaoYouJi_MonBaseType_MaoYouJi_User_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_MaoYouJi_BaseMonster_MaoYouJi_User_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_DestroySystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.MonsterInfoSystem+MaoYouJi_MonsterInfo_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.NpcInfoSystem+MaoYouJi_NpcInfo_MaoYouJi_BaseNpc_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.NpcInfoSystem+MaoYouJi_NpcInfo_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.NpcManageComponentSystem+MaoYouJi_NpcManageComponent_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.RelationComponentSystem+MaoYouJi_RelationComponent_LoadSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.RelationComponentSystem+MaoYouJi_RelationComponent_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserFriendInfoSystem+MaoYouJi_UserFriendInfo_AwakeSystem
2025-05-30 15:16:52.5465 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserFriendInfoSystem+MaoYouJi_UserFriendInfo_MaoYouJi_AddFriendInfo_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserFriendInfoSystem+MaoYouJi_UserFriendInfo_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.RelationManageCompSys+MaoYouJi_RelationManageComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.BenchmarkClientComponentSystem+MaoYouJi_BenchmarkClientComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.BenchmarkServerComponentSystem+MaoYouJi_BenchmarkServerComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.PlayerSystem+MaoYouJi_Player_long_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionPlayerComponentSystem+MaoYouJi_SessionPlayerComponent_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionPlayerComponentSystem+MaoYouJi_SessionPlayerComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.RealmPingGateComponetSystem+MaoYouJi_RealmPingGateComponet_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.RealmPingGateComponetSystem+MaoYouJi_RealmPingGateComponet_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.RobotManagerComponentSystem+MaoYouJi_RobotManagerComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.RobotManagerComponentSystem+MaoYouJi_RobotManagerComponent_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.WatcherComponentSystem+MaoYouJi_WatcherComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.SkillComponentSystem+MaoYouJi_SkillComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.SkillManageComponentSystem+MaoYouJi_SkillManageComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneTaskSystem+MaoYouJi_OneTask_MaoYouJi_BaseTask_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneTaskSystem+MaoYouJi_OneTask_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TaskComponentSystem+MaoYouJi_TaskComponent_LoadSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TaskComponentSystem+MaoYouJi_TaskComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.BiaoCheInfoCompSystem+MaoYouJi_BiaoCheInfoComp_MaoYouJi_MonsterInfo_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.BiaoCheInfoCompSystem+MaoYouJi_BiaoCheInfoComp_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamMemberSystem+MaoYouJi_TeamMember_MaoYouJi_User_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamMemberSystem+MaoYouJi_TeamMember_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamInfoSystem+MaoYouJi_TeamInfo_MaoYouJi_ClientCreateTeamMsg_MaoYouJi_User_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamInfoSystem+MaoYouJi_TeamInfo_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamInfoSystem+MaoYouJi_TeamInfo_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.TeamManageCompSystem+MaoYouJi_TeamManageComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserSystem+MaoYouJi_User_LoadSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserSystem+MaoYouJi_User_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserActorComponentSystem+MaoYouJi_UserActorComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.DailyCntInfoSystem+MaoYouJi_UserDailyCntInfo_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserGetInfoCntSystem+MaoYouJi_UserGetInfoCnt_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserStateCompSys+MaoYouJi_UserStateComp_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneUserStateCompSys+MaoYouJi_OneUserStateComp_MaoYouJi_UserStateDetail_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.OneUserStateCompSys+MaoYouJi_OneUserStateComp_DestroySystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserTimeInfoSystem+MaoYouJi_UserTimeInfo_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.UserVipFuncInfoSystem+MaoYouJi_UserVipFuncInfo_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.FiberUsersComponentSystem+MaoYouJi_FiberUsersComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.VipAfkCompSys+MaoYouJi_VipAfkComp_MaoYouJi_AfkSystemType_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.ConsoleComponentSystem+MaoYouJi_ConsoleComponent_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_System_Net_IPEndPoint_MaoYouJi_NetworkProtocol_AwakeSystem
2025-05-30 15:16:52.5475 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_System_Net_Sockets_AddressFamily_MaoYouJi_NetworkProtocol_AwakeSystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_UpdateSystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.NetComponentSystem+MaoYouJi_NetComponent_DestroySystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionAcceptTimeoutComponentHelper+MaoYouJi_SessionAcceptTimeoutComponent_AwakeSystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionAcceptTimeoutComponentHelper+MaoYouJi_SessionAcceptTimeoutComponent_DestroySystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionIdleCheckerComponentSystem+MaoYouJi_SessionIdleCheckerComponent_AwakeSystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.SessionIdleCheckerComponentSystem+MaoYouJi_SessionIdleCheckerComponent_DestroySystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.PathfindingComponentSystem+MaoYouJi_PathfindingComponent_string_AwakeSystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.PathfindingComponentSystem+MaoYouJi_PathfindingComponent_DestroySystem
2025-05-30 15:16:52.5486 [f-0] EntitySystemSingleton Awake: MaoYouJi.BagGetSystem+MaoYouJi_BagComponent_MaoYouJi_BagType_int_AwakeSystem
2025-05-30 15:16:52.5486 [f-0] CreateCode: MaoYouJi.MessageDispatcher
2025-05-30 15:16:52.5520 [f-0] CreateCode: MaoYouJi.EventSystem
2025-05-30 15:16:52.5560 [f-0] CreateCode: MaoYouJi.HttpDispatcher
2025-05-30 15:16:52.5560 [f-0] CreateCode: MaoYouJi.ConsoleDispatcher
2025-05-30 15:16:52.5566 [f-0] CreateCode: MaoYouJi.MessageSessionDispatcher
2025-05-30 15:16:52.5566 [f-0] CreateCode: MaoYouJi.NumericWatcherComponent
2025-05-30 15:16:52.5605 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartMachineConfigCategory.txt
2025-05-30 15:16:52.5605 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartMachineConfig","_id":1,"InnerIP":"127.0.0.1","OuterIP":"127.0.0.1","WatcherPort":"10000"}],
]}

2025-05-30 15:16:52.5609 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartProcessConfigCategory.txt
2025-05-30 15:16:52.5609 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartProcessConfig","_id":1,"MachineId":1,"Port":20001}],
]}

2025-05-30 15:16:52.5609 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartSceneConfigCategory.txt
2025-05-30 15:16:52.5609 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartSceneConfig","_id":1,"Process":1,"Zone":1,"SceneType":"Realm","Name":"Realm","Port":30002}],
[2, {"_t":"StartSceneConfig","_id":2,"Process":1,"Zone":2,"SceneType":"Gate","Name":"Gate1","Port":30003}],
[3, {"_t":"StartSceneConfig","_id":3,"Process":1,"Zone":2,"SceneType":"Gate","Name":"Gate2","Port":30004}],
[4, {"_t":"StartSceneConfig","_id":4,"Process":1,"Zone":2,"SceneType":"Location","Name":"Location","Port":0}],
[5, {"_t":"StartSceneConfig","_id":5,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map1","Port":30005}],
[6, {"_t":"StartSceneConfig","_id":6,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map2","Port":30006}],
[7, {"_t":"StartSceneConfig","_id":7,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map3","Port":30007}],
[8, {"_t":"StartSceneConfig","_id":8,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map4","Port":30008}],
[9, {"_t":"StartSceneConfig","_id":9,"Process":1,"Zone":2,"SceneType":"Map","Name":"Map5","Port":30009}],
[10, {"_t":"StartSceneConfig","_id":10,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack1","Port":30010}],
[11, {"_t":"StartSceneConfig","_id":11,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack2","Port":30011}],
[12, {"_t":"StartSceneConfig","_id":12,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack3","Port":30012}],
[13, {"_t":"StartSceneConfig","_id":13,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack4","Port":30013}],
[14, {"_t":"StartSceneConfig","_id":14,"Process":1,"Zone":2,"SceneType":"Attack","Name":"Attack5","Port":30014}],
[15, {"_t":"StartSceneConfig","_id":15,"Process":1,"Zone":2,"SceneType":"Social","Name":"Social1","Port":30015}],
[300, {"_t":"StartSceneConfig","_id":300,"Process":1,"Zone":1,"SceneType":"RouterManager","Name":"RouterManager","Port":30300}],
[301, {"_t":"StartSceneConfig","_id":301,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router01","Port":30301}],
[302, {"_t":"StartSceneConfig","_id":302,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router02","Port":30302}],
[303, {"_t":"StartSceneConfig","_id":303,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router03","Port":30303}],
[304, {"_t":"StartSceneConfig","_id":304,"Process":1,"Zone":1,"SceneType":"Router","Name":"Router04","Port":30304}],
]}

2025-05-30 15:16:52.5609 [f-0] configFilePath: ../Config/Json/s/StartConfig/Localhost/StartZoneConfigCategory.txt
2025-05-30 15:16:52.5609 [f-0] output[configType]: {"dict": [
[1, {"_t":"StartZoneConfig","_id":1,"DBConnection":"mongodb://127.0.0.1","DBName":"MaoYouDaLu", "ServerName": "网关服务器"}],
[2, {"_t":"StartZoneConfig","_id":2,"DBConnection":"mongodb://127.0.0.1","DBName":"MaoYouDaLu", "ServerName": "测试服务器", "UserType": "ADMIN", "MaxOnlineNum": 4000}],
]}

2025-05-30 15:16:52.5609 [f-0] configFilePath: ../Config/Json/s/UnitConfigCategory.txt
2025-05-30 15:16:52.5609 [f-0] output[configType]: {
  "dict": []
}
2025-05-30 15:16:52.5823 [f-0] scene create: Main -1 1
2025-05-30 15:16:52.5901 [f--1] AddQueue: -1
2025-05-30 15:16:52.5924 [f--1] scene create: NetInner -2 1
2025-05-30 15:16:52.5981 [f--2] AddQueue: -2
2025-05-30 15:16:52.8279 [f--1] QuartzScheduler start
2025-05-30 15:16:52.8305 [f--1] LocationConfig: Location
2025-05-30 15:16:52.8305 [f--1] scene create: Global -5 1
2025-05-30 15:16:52.8320 [f--5] AddQueue: -5
2025-05-30 15:16:52.8320 [f--5] 初始化全局数据，Zone: 2
2025-05-30 15:16:52.9755 [f--1] mallShopList: 93
2025-05-30 15:16:52.9872 [f--1] comShopList: 277
2025-05-30 15:16:52.9942 [f--1] foodList: 29
2025-05-30 15:16:53.0005 [f--1] taskThingList: 130
2025-05-30 15:16:53.0113 [f--1] treasureList: 139
2025-05-30 15:16:53.0197 [f--1] materialList: 189
2025-05-30 15:16:53.0641 [f--1] equipList: 1558
2025-05-30 15:16:53.0675 [f--1] mineList: 39
2025-05-30 15:16:53.0967 [f--1] monsterList: 237
2025-05-30 15:16:53.1053 [f--1] baseAttackList: 198
2025-05-30 15:16:53.1209 [f--1] baseSkillList: 144
2025-05-30 15:16:53.1628 [f--1] baseTaskList: 518
2025-05-30 15:16:53.1628 [f--1] scene create: Realm 1 1
2025-05-30 15:16:53.1641 [f-1] AddQueue: 1
2025-05-30 15:16:53.1680 [f--1] scene create: Gate 2 1
2025-05-30 15:16:53.1686 [f-2] AddQueue: 2
2025-05-30 15:16:53.1705 [f--1] scene create: Gate 3 1
2025-05-30 15:16:53.1705 [f-3] AddQueue: 3
2025-05-30 15:16:53.1717 [f--1] scene create: Location 4 1
2025-05-30 15:16:53.1731 [f-4] AddQueue: 4
2025-05-30 15:16:53.1741 [f--1] scene create: Map 5 1
2025-05-30 15:16:53.1753 [f-5] AddQueue: 5
2025-05-30 15:16:53.1790 [f--1] scene create: Map 6 1
2025-05-30 15:16:53.1797 [f-6] AddQueue: 6
2025-05-30 15:16:53.1797 [f--1] scene create: Map 7 1
2025-05-30 15:16:53.1807 [f-7] AddQueue: 7
2025-05-30 15:16:53.1816 [f--1] scene create: Map 8 1
2025-05-30 15:16:53.1816 [f-8] AddQueue: 8
2025-05-30 15:16:53.1827 [f--1] scene create: Map 9 1
2025-05-30 15:16:53.1827 [f-9] AddQueue: 9
2025-05-30 15:16:53.1840 [f--1] scene create: Attack 10 1
2025-05-30 15:16:53.1848 [f-10] AddQueue: 10
2025-05-30 15:16:53.1848 [f--1] scene create: Attack 11 1
2025-05-30 15:16:53.1864 [f-11] AddQueue: 11
2025-05-30 15:16:53.1866 [f--1] scene create: Attack 12 1
2025-05-30 15:16:53.1866 [f-12] AddQueue: 12
2025-05-30 15:16:53.1877 [f--1] scene create: Attack 13 1
2025-05-30 15:16:53.1926 [f-13] AddQueue: 13
2025-05-30 15:16:53.1937 [f--1] scene create: Attack 14 1
2025-05-30 15:16:53.1949 [f-14] AddQueue: 14
2025-05-30 15:16:53.1949 [f--1] scene create: Social 15 1
2025-05-30 15:16:53.1966 [f-15] AddQueue: 15
2025-05-30 15:16:53.1966 [f--1] scene create: RouterManager 300 1
2025-05-30 15:16:53.2205 [f--1] scene create: Router 301 1
2025-05-30 15:16:53.2275 [f-301] Router create: 301
2025-05-30 15:16:53.2283 [f--1] scene create: Router 302 1
2025-05-30 15:16:53.2296 [f-302] Router create: 302
2025-05-30 15:16:53.2296 [f--1] scene create: Router 303 1
2025-05-30 15:16:53.2309 [f-303] Router create: 303
2025-05-30 15:16:53.2309 [f--1] scene create: Router 304 1
2025-05-30 15:16:53.2318 [f-304] Router create: 304
2025-05-30 15:16:53.4653 [f-7] 地图节点加载开始，地图节点总数：2040
2025-05-30 15:16:53.4677 [f-6] 地图节点加载开始，地图节点总数：2040
2025-05-30 15:16:53.4688 [f-8] 地图节点加载开始，地图节点总数：2040
2025-05-30 15:16:53.4697 [f-5] 地图节点加载开始，地图节点总数：2040
2025-05-30 15:16:53.4697 [f-9] 地图节点加载开始，地图节点总数：2040
2025-05-30 15:16:53.5207 [f-5] Map1 地图节点加载完成，当前地图节点数量：60
2025-05-30 15:16:53.7471 [f-6] Map2 地图节点加载完成，当前地图节点数量：484
2025-05-30 15:16:53.7519 [f-9] Map5 地图节点加载完成，当前地图节点数量：410
2025-05-30 15:16:53.7625 [f-8] Map4 地图节点加载完成，当前地图节点数量：555
2025-05-30 15:16:53.7672 [f-7] 地图点构建完成：47
2025-05-30 15:16:53.9555 [f-7] 地图距离计算完成：2040
2025-05-30 15:16:53.9585 [f-7] 城市距离计算完成：35
2025-05-30 15:16:53.9585 [f-7] Map3 地图节点加载完成，当前地图节点数量：531
2025-05-30 15:17:59.9018 [f-302] router new: outerConn: 3275639087 innerConn: 0 127.0.0.1:53104
2025-05-30 15:17:59.9018 [f-302] router create: 127.0.0.1:30002 3275639087 0 127.0.0.1:53104
2025-05-30 15:17:59.9564 [f-302] kcp router syn: 3275639087 0 127.0.0.1:30002 127.0.0.1:53104
2025-05-30 15:17:59.9579 [f-1] channel create: 2147483650 3275639087 127.0.0.1:61841 Accept
2025-05-30 15:17:59.9599 [f-1] session create: zone: 1 id: 2147483650 1748589479959 
2025-05-30 15:17:59.9599 [f-1] kservice syn: 2147483650 3275639087 2147483650 127.0.0.1:61841
2025-05-30 15:17:59.9616 [f-302] kcp router ack: 3275639087 2147483650 127.0.0.1:53104
2025-05-30 15:18:00.4023 [f-1][r-1537675298] tap tap login result: 0 cQPq2VZTPtrQUKHhblcZeg== Wd8SUnqB8IRT9hmXibjLOQ==
2025-05-30 15:18:01.2791 [f-304] router new: outerConn: 3676367093 innerConn: 0 127.0.0.1:53106
2025-05-30 15:18:01.2791 [f-304] router create: 127.0.0.1:30004 3676367093 0 127.0.0.1:53106
2025-05-30 15:18:01.2960 [f-304] kcp router syn: 3676367093 0 127.0.0.1:30004 127.0.0.1:53106
2025-05-30 15:18:01.2960 [f-302] kcp router outer fin: 3275639087 2147483650 127.0.0.1:30002
2025-05-30 15:18:01.2960 [f-302] kcp router outer fin: 3275639087 2147483650 127.0.0.1:30002
2025-05-30 15:18:01.2960 [f-302] kcp router outer fin: 3275639087 2147483650 127.0.0.1:30002
2025-05-30 15:18:01.2966 [f-1] kservice recv fin: 2147483650 3275639087 0
2025-05-30 15:18:01.2966 [f-3] channel create: 2147483652 3676367093 127.0.0.1:62982 Accept
2025-05-30 15:18:01.2966 [f-3] session create: zone: 2 id: 2147483652 1748589481296 
2025-05-30 15:18:01.2966 [f-3] kservice syn: 2147483652 3676367093 2147483652 127.0.0.1:62982
2025-05-30 15:18:01.2966 [f-304] kcp router ack: 3676367093 2147483652 127.0.0.1:53106
2025-05-30 15:18:01.2974 [f-1] channel dispose: 2147483650 3275639087 100208
2025-05-30 15:18:01.2985 [f-1] session dispose: 127.0.0.1:53104 id: 2147483650 ErrorCode: 100208, please see ErrorCode.cs! 1748589481298
2025-05-30 15:18:02.7282 [f-3][r-1537871910] 转移用户: 1238372941060900 462113922426673196 3 9
2025-05-30 15:18:08.9171 [f-302] TChannel OnError: 100208 127.0.0.1:53104
2025-05-30 15:18:08.9171 [f-304] TChannel OnError: 100208 127.0.0.1:53106
2025-05-30 15:18:08.9176 [f-302] channel dispose: 2147483649 127.0.0.1:53104 0
2025-05-30 15:18:08.9176 [f-304] channel dispose: 2147483651 127.0.0.1:53106 0
