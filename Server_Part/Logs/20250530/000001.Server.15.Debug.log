2025-05-30 15:16:52.3376 (Init.cs:27) [f-0] Start World
2025-05-30 15:16:52.3502 (CodeLoader.cs:56) [f-0] dllBytes: 806912, pdbBytes: 305216
2025-05-30 15:16:55.1703 (MessageQueue.cs:32) [f-1][r-1533480961] Send: 2 1 1
2025-05-30 15:16:55.1747 (MessageQueue.cs:32) [f-2][r-1533480961] Send: 1 1 0
2025-05-30 15:16:57.1794 (MessageQueue.cs:32) [f-1][r-1533612034] Send: 3 1 1
2025-05-30 15:16:57.1806 (MessageQueue.cs:32) [f-3][r-1533612034] Send: 1 1 0
2025-05-30 15:16:59.1819 (MessageQueue.cs:32) [f-1][r-1533743107] Send: 2 1 1
2025-05-30 15:16:59.1819 (MessageQueue.cs:32) [f-2][r-1533743107] Send: 1 1 0
2025-05-30 15:17:01.1821 (MessageQueue.cs:32) [f-1][r-1533874180] Send: 3 1 1
2025-05-30 15:17:01.1831 (MessageQueue.cs:32) [f-3][r-1533874180] Send: 1 1 0
2025-05-30 15:17:03.1837 (MessageQueue.cs:32) [f-1][r-1534005253] Send: 3 1 1
2025-05-30 15:17:03.1847 (MessageQueue.cs:32) [f-3][r-1534005253] Send: 1 1 0
2025-05-30 15:17:05.1852 (MessageQueue.cs:32) [f-1][r-1534136326] Send: 2 1 1
2025-05-30 15:17:05.1859 (MessageQueue.cs:32) [f-2][r-1534136326] Send: 1 1 0
2025-05-30 15:17:07.1864 (MessageQueue.cs:32) [f-1][r-1534267399] Send: 2 1 1
2025-05-30 15:17:07.1867 (MessageQueue.cs:32) [f-2][r-1534267399] Send: 1 1 0
2025-05-30 15:17:09.1863 (MessageQueue.cs:32) [f-1][r-1534398472] Send: 2 1 1
2025-05-30 15:17:09.1872 (MessageQueue.cs:32) [f-2][r-1534398472] Send: 1 1 0
2025-05-30 15:17:11.1879 (MessageQueue.cs:32) [f-1][r-1534529545] Send: 2 1 1
2025-05-30 15:17:11.1879 (MessageQueue.cs:32) [f-2][r-1534529545] Send: 1 1 0
2025-05-30 15:17:13.1890 (MessageQueue.cs:32) [f-1][r-1534660618] Send: 3 1 1
2025-05-30 15:17:13.1901 (MessageQueue.cs:32) [f-3][r-1534660618] Send: 1 1 0
2025-05-30 15:17:15.1920 (MessageQueue.cs:32) [f-1][r-1534791691] Send: 3 1 1
2025-05-30 15:17:15.1927 (MessageQueue.cs:32) [f-3][r-1534791691] Send: 1 1 0
2025-05-30 15:17:17.1948 (MessageQueue.cs:32) [f-1][r-1534922764] Send: 3 1 1
2025-05-30 15:17:17.1948 (MessageQueue.cs:32) [f-3][r-1534922764] Send: 1 1 0
2025-05-30 15:17:19.1968 (MessageQueue.cs:32) [f-1][r-1535053837] Send: 3 1 1
2025-05-30 15:17:19.1978 (MessageQueue.cs:32) [f-3][r-1535053837] Send: 1 1 0
2025-05-30 15:17:21.1997 (MessageQueue.cs:32) [f-1][r-1535184910] Send: 3 1 1
2025-05-30 15:17:21.2007 (MessageQueue.cs:32) [f-3][r-1535184910] Send: 1 1 0
2025-05-30 15:17:23.2008 (MessageQueue.cs:32) [f-1][r-1535315983] Send: 3 1 1
2025-05-30 15:17:23.2008 (MessageQueue.cs:32) [f-3][r-1535315983] Send: 1 1 0
2025-05-30 15:17:25.2030 (MessageQueue.cs:32) [f-1][r-1535447056] Send: 3 1 1
2025-05-30 15:17:25.2037 (MessageQueue.cs:32) [f-3][r-1535447056] Send: 1 1 0
2025-05-30 15:17:27.2041 (MessageQueue.cs:32) [f-1][r-1535578129] Send: 3 1 1
2025-05-30 15:17:27.2052 (MessageQueue.cs:32) [f-3][r-1535578129] Send: 1 1 0
2025-05-30 15:17:29.2059 (MessageQueue.cs:32) [f-1][r-1535709202] Send: 3 1 1
2025-05-30 15:17:29.2070 (MessageQueue.cs:32) [f-3][r-1535709202] Send: 1 1 0
2025-05-30 15:17:31.2096 (MessageQueue.cs:32) [f-1][r-1535840275] Send: 2 1 1
2025-05-30 15:17:31.2108 (MessageQueue.cs:32) [f-2][r-1535840275] Send: 1 1 0
2025-05-30 15:17:33.2126 (MessageQueue.cs:32) [f-1][r-1535971348] Send: 2 1 1
2025-05-30 15:17:33.2136 (MessageQueue.cs:32) [f-2][r-1535971348] Send: 1 1 0
2025-05-30 15:17:35.2146 (MessageQueue.cs:32) [f-1][r-1536102421] Send: 2 1 1
2025-05-30 15:17:35.2146 (MessageQueue.cs:32) [f-2][r-1536102421] Send: 1 1 0
2025-05-30 15:17:37.2163 (MessageQueue.cs:32) [f-1][r-1536233494] Send: 2 1 1
2025-05-30 15:17:37.2167 (MessageQueue.cs:32) [f-2][r-1536233494] Send: 1 1 0
2025-05-30 15:17:39.2178 (MessageQueue.cs:32) [f-1][r-1536364567] Send: 3 1 1
2025-05-30 15:17:39.2191 (MessageQueue.cs:32) [f-3][r-1536364567] Send: 1 1 0
2025-05-30 15:17:41.2198 (MessageQueue.cs:32) [f-1][r-1536495640] Send: 3 1 1
2025-05-30 15:17:41.2205 (MessageQueue.cs:32) [f-3][r-1536495640] Send: 1 1 0
2025-05-30 15:17:43.2207 (MessageQueue.cs:32) [f-1][r-1536626713] Send: 2 1 1
2025-05-30 15:17:43.2217 (MessageQueue.cs:32) [f-2][r-1536626713] Send: 1 1 0
2025-05-30 15:17:45.2218 (MessageQueue.cs:32) [f-1][r-1536757786] Send: 2 1 1
2025-05-30 15:17:45.2228 (MessageQueue.cs:32) [f-2][r-1536757786] Send: 1 1 0
2025-05-30 15:17:47.2246 (MessageQueue.cs:32) [f-1][r-1536888859] Send: 2 1 1
2025-05-30 15:17:47.2246 (MessageQueue.cs:32) [f-2][r-1536888859] Send: 1 1 0
2025-05-30 15:17:49.2238 (MessageQueue.cs:32) [f-1][r-1537019932] Send: 3 1 1
2025-05-30 15:17:49.2238 (MessageQueue.cs:32) [f-3][r-1537019932] Send: 1 1 0
2025-05-30 15:17:51.2271 (MessageQueue.cs:32) [f-1][r-1537151005] Send: 3 1 1
2025-05-30 15:17:51.2280 (MessageQueue.cs:32) [f-3][r-1537151005] Send: 1 1 0
2025-05-30 15:17:53.2298 (MessageQueue.cs:32) [f-1][r-1537282078] Send: 2 1 1
2025-05-30 15:17:53.2308 (MessageQueue.cs:32) [f-2][r-1537282078] Send: 1 1 0
2025-05-30 15:17:55.2319 (MessageQueue.cs:32) [f-1][r-1537413151] Send: 2 1 1
2025-05-30 15:17:55.2335 (MessageQueue.cs:32) [f-2][r-1537413151] Send: 1 1 0
2025-05-30 15:17:57.2330 (MessageQueue.cs:32) [f-1][r-1537544224] Send: 3 1 1
2025-05-30 15:17:57.2340 (MessageQueue.cs:32) [f-3][r-1537544224] Send: 1 1 0
2025-05-30 15:17:59.2353 (MessageQueue.cs:32) [f-1][r-1537675297] Send: 2 1 1
2025-05-30 15:17:59.2356 (MessageQueue.cs:32) [f-2][r-1537675297] Send: 1 1 0
2025-05-30 15:17:59.9733 (NetComponentSystem.cs:78) TapTapLogInReq
2025-05-30 15:17:59.9792 (C2R_LoginHandler.cs:14) [f-1][r-**********] C2R_LoginHandler: { "_t" : "TapTapLogInReq", "RpcId" : 1, "RequestId" : **********, "UserId" : NumberLong(0), "MsgTime" : NumberLong(0), "OwnerFiberId" : 0, "tapTapAccessToken" : { "kid" : "1/XvhIe4j68Ds7OBfaAx_3BDs-qa_cUEF_J7lT5x7dJjNRAgRshMqPTxe0zV-CzTXv_rHlHfrJ4bt8aprNJoo_x9Ts0Aby1_O7GqOm6Adjp3lRDUGBGMjQouN-5UhZTesbDHJlt-XwBHKa-hOSgn9rs6Bli0Iwr5mIy_619lTyXxBh9Boa4DwXvDf_BVpfqVaZqrkyYdj-iODqz4ortDpBaCCFR4f-g38vZLDA8UU8WJkfw_nKUuwqHbLgrmHhMEf-GdqsGdfAUkc8TBLT41p7FYdY2Ir4UVmlMdjumtzpdKHc1CeOMIRtV-7r78nH4d2jqSTjGyeQJscr7OD5deEtbA", "tokenType" : "mac", "macKey" : "3iZZhf9yADigtLhVd9omGgFdhQyEl4HBp9tKeaan", "macAlgorithm" : "hmac-sha-1", "openId" : "cQPq2VZTPtrQUKHhblcZeg==", "unionId" : "Wd8SUnqB8IRT9hmXibjLOQ==" }, "isReconnet" : false, "accountId" : NumberLong(0), "accessKey" : NumberLong(0) }
2025-05-30 15:18:00.4089 (C2R_LoginHandler.cs:27) [f-1][r-**********] preAccount: MaoYouJi.NetAccount
2025-05-30 15:18:00.4333 (C2R_LoginHandler.cs:82) [f-1][r-**********] gate address: System.Collections.Generic.List`1[MaoYouJi.StartZoneConfig]
2025-05-30 15:18:00.4339 (Session.cs:154) MaoYouJi.TapTapLogInResp
2025-05-30 15:18:01.2363 (MessageQueue.cs:32) [f-1][r-**********] Send: 2 1 1
2025-05-30 15:18:01.2366 (MessageQueue.cs:32) [f-2][r-**********] Send: 1 1 0
2025-05-30 15:18:01.2576 (NetComponentSystem.cs:78) SelectServerReq
2025-05-30 15:18:01.2610 (MessageQueue.cs:32) [f-1][r-**********] Send: 3 1 1
2025-05-30 15:18:01.2627 (R2G_GetLoginKeyHandler.cs:11) [f-3] R2G_GetLoginKeyHandler: { "_t" : "R2G_GetLoginKey", "RpcId" : 35, "AccountId" : NumberLong("****************") }
2025-05-30 15:18:01.2627 (MessageQueue.cs:32) [f-3] Send: 1 1 0
2025-05-30 15:18:01.2641 (Session.cs:154) MaoYouJi.SelectServerResp
2025-05-30 15:18:01.2966 (KService.cs:459) [f-1] kservice remove channel: ********** ********** ********** 100208
2025-05-30 15:18:01.3596 (NetComponentSystem.cs:78) GateLogInReq
2025-05-30 15:18:01.3820 (Session.cs:154) MaoYouJi.GateLogInResp
2025-05-30 15:18:02.6929 (NetComponentSystem.cs:78) StartGameReq
2025-05-30 15:18:02.7369 (Session.cs:154) MaoYouJi.StartGameResp
2025-05-30 15:18:03.2381 (MessageQueue.cs:32) [f-1][r-**********] Send: 2 1 1
2025-05-30 15:18:03.2392 (MessageQueue.cs:32) [f-2][r-**********] Send: 1 1 0
2025-05-30 15:18:03.3007 (NetComponentSystem.cs:78) C2G_Ping
2025-05-30 15:18:03.3007 (Session.cs:154) MaoYouJi.G2C_Ping
2025-05-30 15:18:04.2022 (NetComponentSystem.cs:78) GetAnnouceReq
2025-05-30 15:18:04.2029 (MessageQueue.cs:32) [f-3][r-**********] Send: -5 1 462113621778890765
2025-05-30 15:18:04.2055 (MessageQueue.cs:32) [f--5][r-**********] Send: 3 1 0
2025-05-30 15:18:05.2384 (MessageQueue.cs:32) [f-1][r-1538068521] Send: 2 1 1
2025-05-30 15:18:05.2384 (MessageQueue.cs:32) [f-2][r-1538068521] Send: 1 1 0
2025-05-30 15:18:05.3315 (NetComponentSystem.cs:78) C2G_Ping
2025-05-30 15:18:05.3315 (Session.cs:154) MaoYouJi.G2C_Ping
2025-05-30 15:18:07.2397 (MessageQueue.cs:32) [f-1][r-1538199594] Send: 3 1 1
2025-05-30 15:18:07.2397 (MessageQueue.cs:32) [f-3][r-1538199594] Send: 1 1 0
2025-05-30 15:18:07.3680 (NetComponentSystem.cs:78) C2G_Ping
2025-05-30 15:18:07.3684 (Session.cs:154) MaoYouJi.G2C_Ping
2025-05-30 15:18:09.2406 (MessageQueue.cs:32) [f-1][r-1538330667] Send: 3 1 1
2025-05-30 15:18:09.2419 (MessageQueue.cs:32) [f-3][r-1538330667] Send: 1 1 0
2025-05-30 15:18:11.2435 (MessageQueue.cs:32) [f-1][r-1538461740] Send: 2 1 1
2025-05-30 15:18:11.2454 (MessageQueue.cs:32) [f-2][r-1538461740] Send: 1 1 0
2025-05-30 15:18:13.2457 (MessageQueue.cs:32) [f-1][r-1538592813] Send: 3 1 1
2025-05-30 15:18:13.2457 (MessageQueue.cs:32) [f-3][r-1538592813] Send: 1 1 0
