2025-05-30 15:16:52.6350 INFO [Quartz.Impl.StdSchedulerFactory] Default Quartz.NET properties loaded from embedded resource file 
2025-05-30 15:16:52.8235 DEBUG [Quartz.Simpl.TaskSchedulingThreadPool] TaskSchedulingThreadPool configured with max concurrency of 10 and TaskScheduler ThreadPoolTaskScheduler. 
2025-05-30 15:16:52.8255 INFO [Quartz.Core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-05-30 15:16:52.8255 INFO [Quartz.Core.QuartzScheduler] Quartz Scheduler created 
2025-05-30 15:16:52.8255 INFO [Quartz.Simpl.RAMJobStore] RAMJobStore initialized. 
2025-05-30 15:16:52.8255 INFO [Quartz.Impl.StdSchedulerFactory] Quartz Scheduler 3.14.0.0 - 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED' initialized 
2025-05-30 15:16:52.8255 INFO [Quartz.Impl.StdSchedulerFactory] Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10 
2025-05-30 15:16:52.8255 INFO [Quartz.Impl.StdSchedulerFactory] Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False 
2025-05-30 15:16:52.8279 INFO [Quartz.Core.QuartzScheduler] Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started. 
2025-05-30 15:16:52.8286 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 15:17:18.1081 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 15:17:43.5357 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 15:18:07.7593 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
