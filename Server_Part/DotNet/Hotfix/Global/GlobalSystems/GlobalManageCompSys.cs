using System;
using System.Collections.Generic;
using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(GlobalManageComp))]
  [FriendOf(typeof(GlobalManageComp))]
  public static partial class GlobalManageCompSys
  {
    [EntitySystem]
    private static void Awake(this GlobalManageComp self)
    {
      ETLog.Info($"初始化全局数据，Zone: {self.Zone()}");
      self.Init().Coroutine();
    }

    private static async ETTask Init(this GlobalManageComp self)
    {
      Config config = await self.LoadConfig();
      self.Root().AddComponent<DaTaoShaActComp, DaTaoShaActConf>(config.daTaoShaConf);
    }

    /**
   * 获取所有公告信息
   * 
   * @return 所有公告列表
   */
    public static async ETTask<List<AnnouncementInfo>> GetAllAnnouncements(this GlobalManageComp self)
    {
      try
      {
        DBManagerComponent dbManagerComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>();
        DBComponent dbComponent = dbManagerComponent.GetZoneDB(1);
        List<AnnouncementInfo> announcements = await dbComponent.QueryClass<AnnouncementInfo>(n => n.isActive);
        announcements.Sort((a, b) =>
        {
          if (a.isTop && !b.isTop)
          {
            return -1;
          }
          if (!a.isTop && b.isTop)
          {
            return 1;
          }
          return b.createTime.CompareTo(a.createTime);
        });
        return announcements;
      }
      catch (Exception e)
      {
        ETLog.Error($"获取公告列表失败: {e}");
        return new List<AnnouncementInfo>();
      }
    }

    public static async ETTask<Config> LoadConfig(this GlobalManageComp self)
    {
      DBManagerComponent dbManagerComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>();
      DBComponent dbComponent = dbManagerComponent.GetMyZoneDB();
      FilterDefinitionBuilder<Config> filter = new();
      Config config = await dbComponent.QueryClass<Config>(filter.Eq(n => n.Id, 8888));
      GlobalInfoCache.Instance.Config = config;
      return config;
    }
  }

}
